import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Menu } from "primereact/menu";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Calendar } from "primereact/calendar";
import { ChartSortDropdown, createSortOptions, sortData } from "./components/ChartSortDropdown";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

// Default data if no supplyData is provided
const defaultData = [
  { month: "Jan 2024", environmentScore: 15.2, supplierCount: 5 },
  { month: "Feb 2024", environmentScore: 18.7, supplierCount: 8 },
  { month: "Mar 2024", environmentScore: 22.1, supplierCount: 12 },
  { month: "Apr 2024", environmentScore: 19.8, supplierCount: 10 },
  { month: "May 2024", environmentScore: 25.3, supplierCount: 15 },
  { month: "Jun 2024", environmentScore: 28.9, supplierCount: 18 },
];

// Custom Legend Component
const CustomLegend = (props) => {
  const { payload } = props;
  return (
    <div style={{ display: "flex", justifyContent: "center", marginBottom: "10px" }}>
      {payload.map((entry, index) => (
        <div key={index} style={{ display: "flex", alignItems: "center", marginRight: "20px" }}>
          <div
            style={{
              width: "12px",
              height: "12px",
              backgroundColor: entry.color,
              marginRight: "5px",
            }}
          />
          <span style={{ fontSize: "12px", color: "#333" }}>{entry.value}</span>
        </div>
      ))}
    </div>
  );
};

// Custom Label Component for displaying supplier count on data points
const CustomLabel = ({ x, y, value, payload, dataKey }) => {
  if (!payload || value === null || value === undefined) return null;
  
  const supplierCount = payload.supplierCount || 0;
  
  return (
    <g>
      {/* Background circle for better visibility */}
      <circle
        cx={x}
        cy={y - 15}
        r="8"
        fill="white"
        stroke="#22C55E"
        strokeWidth="1"
        opacity="0.9"
      />
      <text
        x={x}
        y={y - 14}
        fill="#22C55E"
        textAnchor="middle"
        fontSize="9"
        fontWeight="600"
        dominantBaseline="middle"
      >
        {supplierCount}
      </text>
    </g>
  );
};

const EnvironmentLineDemo = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const menuRef = useRef(null);
  const tableRef = useRef(null);
  const chartRef = useRef(null);
  const [visibleSeries, setVisibleSeries] = useState({
    environmentScore: true,
  });

  // Chart data state
  const [chartData, setChartData] = useState(defaultData);
  const [filteredChartData, setFilteredChartData] = useState(defaultData);
  
  // Date filter state
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  // Latest score state
  const [latestEnvironmentScore, setLatestEnvironmentScore] = useState(null);

  // Sorting state
  const [sortField, setSortField] = useState('none');

  // Memoize sorting options to prevent re-creation
  const sortOptions = useMemo(() => createSortOptions([
    { name: 'month', label: 'Month', type: 'string' },
    { name: 'environmentScore', label: 'Environment Score', type: 'number' },
  ]), []);

  // Handle sort change - memoized to prevent re-creation
  const handleSortChange = useCallback((e) => {
    const selectedSort = e.value;
    
    setSortField(selectedSort);
    
    // Apply sorting to the data
    setChartData(prevData => sortData(prevData, selectedSort));
  }, []);

  // Memoize Line component props to prevent re-creation
  const lineProps = useMemo(() => ({
    environmentScore: {
      dataKey: "environmentScore",
      stroke: "#22C55E",
      strokeWidth: 2,
      name: "Environment Score",
      type: "monotone"
    }
  }), []);

  // Function to calculate latest environment score based on filtered data
  const calculateLatestEnvironmentScore = useCallback((filteredData) => {
    if (!filteredData.length) {
      return null;
    }

    // Group supply data by vendor code
    const groupedByVendor = {};
    filteredData.forEach(supplier => {
      if (supplier.vendor_code && supplier.environment !== undefined && supplier.environment !== null) {
        const vendorCode = supplier.vendor_code.toString();
        if (!groupedByVendor[vendorCode]) {
          groupedByVendor[vendorCode] = [];
        }
        groupedByVendor[vendorCode].push(supplier);
      }
    });

    // Get latest environment score for each vendor
    const latestScores = [];
    Object.keys(groupedByVendor).forEach(vendorCode => {
      const vendorSuppliers = groupedByVendor[vendorCode];

      // Sort by audit_start_date (most recent first) and get the latest
      const latestSupplier = vendorSuppliers.sort((a, b) => {
        if (!a.audit_start_date || !b.audit_start_date) return 0;

        const dateA = a.audit_start_date.split('.').reverse().join('-');
        const dateB = b.audit_start_date.split('.').reverse().join('-');

        return new Date(dateB) - new Date(dateA);
      })[0];

      latestScores.push(parseFloat(latestSupplier.environment));
    });

    // Calculate average of latest scores
    if (latestScores.length === 0) {
      return null;
    }

    const averageScore = latestScores.reduce((sum, score) => sum + score, 0) / latestScores.length;
    return averageScore;
  }, []);

  // Process supply data to create chart data
  useEffect(() => {
    if (!supplyData || supplyData.length === 0) {
      setChartData(defaultData);
      setFilteredChartData(defaultData);
      return;
    }

    try {
      // Group data by month-year and calculate average scores
      const monthlyData = {};
      const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

      // Create a map of vendor codes from supplyData for filtering
      const supplierVendorCodes = new Set(supplyData.map(supplier => supplier.vendor_code?.toString()));

      // Process supply data for environment scores
      supplyData.forEach(supplier => {
        if (supplier.audit_start_date && supplier.environment !== undefined && supplier.environment !== null) {
          // Parse audit_start_date (format: "DD.MM.YYYY")
          const dateParts = supplier.audit_start_date.split('.');
          if (dateParts.length === 3) {
            const day = parseInt(dateParts[0]);
            const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
            const year = parseInt(dateParts[2]);
            
            const auditDate = new Date(year, month, day);
            const monthName = months[month];
            const monthYearKey = `${monthName} ${year}`;

            if (!monthlyData[monthYearKey]) {
              monthlyData[monthYearKey] = {
                month: monthYearKey,
                monthName: monthName,
                year: year,
                environmentScore: 0,
                count: 0,
                suppliers: new Set()
              };
            }

            monthlyData[monthYearKey].environmentScore += parseFloat(supplier.environment);
            monthlyData[monthYearKey].count += 1;
            monthlyData[monthYearKey].suppliers.add(supplier.vendor_code?.toString());
          }
        }
      });

      // Calculate averages and supplier counts
      const processedData = Object.values(monthlyData).map(item => {
        return {
          month: item.month,
          monthName: item.monthName,
          year: item.year,
          environmentScore: item.count > 0 ?
            parseFloat(item.environmentScore / item.count).toFixed(2) : null,
          supplierCount: item.suppliers.size,
        };
      });

      // Sort by year and month
      processedData.sort((a, b) => {
        if (a.year !== b.year) {
          return a.year - b.year;
        }
        const monthOrder = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        return monthOrder.indexOf(a.monthName) - monthOrder.indexOf(b.monthName);
      });

      console.log('EnvironmentLineDemo - Processed Data:', processedData);

      if (processedData.length > 0) {
        setChartData(processedData);
        setFilteredChartData(processedData);
      } else {
        setChartData(defaultData);
        setFilteredChartData(defaultData);
      }

    } catch (error) {
      console.error('Error processing environment data:', error);
      setChartData(defaultData);
      setFilteredChartData(defaultData);
    }
  }, [supplyData]);

  // Update latest scores when supply data changes
  useEffect(() => {
    const latestEnvironment = calculateLatestEnvironmentScore(supplyData);
    setLatestEnvironmentScore(latestEnvironment);

    console.log('EnvironmentLineDemo - Latest Environment Score:', latestEnvironment);
  }, [supplyData, calculateLatestEnvironmentScore]);

  // Filter data based on date range
  useEffect(() => {
    if (!startDate || !endDate) {
      setFilteredChartData(chartData);
      return;
    }

    const filtered = chartData.filter(item => {
      const [monthName, year] = item.month.split(' ');
      const monthIndex = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"].indexOf(monthName);
      const itemDate = new Date(parseInt(year), monthIndex, 1);
      
      return itemDate >= startDate && itemDate <= endDate;
    });

    setFilteredChartData(filtered);
  }, [startDate, endDate, chartData]);

  const menuItems = [
    {
      label: "Chart View",
      icon: "pi pi-chart-line",
      command: () => setActiveMode(true),
    },
    {
      label: "Table View",
      icon: "pi pi-table",
      command: () => setActiveMode(false),
    },
  ];

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #22C55E", paddingBottom: "5px" }}>Environment Score Trends</h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            <Calendar
              value={startDate}
              onChange={(e) => setStartDate(e.value)}
              placeholder="Start Date"
              dateFormat="mm/yy"
              view="month"
              style={{ width: "120px" }}
            />
            <Calendar
              value={endDate}
              onChange={(e) => setEndDate(e.value)}
              placeholder="End Date"
              dateFormat="mm/yy"
              view="month"
              style={{ width: "120px" }}
            />
          </div>
          <ChartSortDropdown
            value={sortField}
            options={sortOptions}
            onChange={handleSortChange}
            placeholder="Sort by..."
          />
          <Menu model={menuItems} popup ref={menuRef} />
          <Button
            icon="pi pi-bars"
            className="p-button-text"
            onClick={(event) => menuRef.current.toggle(event)}
          />
        </div>
      </div>

      <div style={{ display: "flex", justifyContent: "space-between", marginBottom: "20px" }}>
        <div style={{ textAlign: "center" }}>
          <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Latest Environment Score</p>
          <p style={{ margin: "0", fontSize: "24px", fontWeight: "bold", color: "#22C55E" }}>
            {latestEnvironmentScore !== null ? latestEnvironmentScore.toFixed(2) : "N/A"}
          </p>
        </div>
      </div>

      {activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={filteredChartData} ref={chartRef} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip
              formatter={(value, name, props) => {
                const { payload } = props;
                return [
                  value ? `${value}` : 'N/A',
                  `${name} (${payload.supplierCount || 0} suppliers)`
                ];
              }}
              labelFormatter={(label) => `Month: ${label}`}
            />
            <Legend content={CustomLegend} />
            <Line
              {...lineProps.environmentScore}
              hide={!visibleSeries.environmentScore}
              connectNulls={false}
              dot={{ r: 4 }}
              label={(props) => <CustomLabel {...props} dataKey="environmentScore" />}
            />
          </LineChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={filteredChartData} paginator rows={10} ref={tableRef} sortMode="multiple">
            <Column field="month" header="Month" sortable />
            <Column
              field="environmentScore"
              header="Environment Score"
              sortable
              body={(rowData) => rowData.environmentScore || "N/A"}
            />
            <Column field="supplierCount" header="Supplier Count" sortable />
          </DataTable>
        </div>
      )}
    </Card>
  );
};

export default EnvironmentLineDemo;
