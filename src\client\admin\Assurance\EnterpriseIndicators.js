import React, { useEffect, useRef, useState } from 'react'
import APIServices from '../../../service/APIService'
import { API } from '../../../constants/api_url'
import { red } from '@mui/material/colors';
import { MultiSelect } from 'primereact/multiselect';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { useSelector } from 'react-redux';
import { filterDataByTierAndLocationByLevel, getFiscalYearsFromStartDate } from '../../../components/BGHF/helper';
import {
    Tab,
    Tabs,
    Box,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
} from "@mui/material";
import { Dropdown } from 'primereact/dropdown';
import { Badge } from 'primereact/badge';
import XlsxPopulate from 'xlsx-populate';

export const EnterpriseIndicator = ({ admin }) => {
    const [yearOption, setYearOption] = useState([]);
    const [customMetricResponseBk, setCustomMetricResponseBk] = useState([]);
    const [customMetricResponse, setCustomMetricResponse] = useState([]);
    const [metricsData, setMetricsData] = useState([]);
    const [metricsDataBk, setMetricsDataBk] = useState([]);
    const [indidcatorlist, setIndicatorList] = useState([])
    const [indifilter, setIndiFilter] = useState({ year: 0, section: 0, indicator: 0, form: 0, framework: [], entity: [] })
    const [load, setLoad] = useState([]);
    const [assFramework, setAssFramework] = useState([])
    const [rawsitelist, setRawsitelist] = useState([]);
    const [locList, setLocList] = useState({ country: [], city: [], location: [] })
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);
    const tableRef = useRef(null)
    useEffect(() => {
        renderData()
    }, [])
    const renderData = async () => {
        let yrOptions = getFiscalYearsFromStartDate(
            admin.information.startdate, fymonth
        );
        setLoad(true)
        setYearOption(yrOptions)
        try {

            if (yrOptions.length) {
                const promise3 = await APIServices.post(API.GetAssignedIndicator_UP(admin.id))
                setIndicatorList(promise3.data)
                let uriStringLoc = {
                    include: [
                        {
                            relation: "locationTwos",
                            scope: { include: [{ relation: "locationThrees" }] },
                        },
                    ],
                };
                const promise0 = APIServices.get(API.Report_Name_Twos)
                const promise1 = APIServices.get(
                    API.LocationOne_UP(admin.id) +
                    `?filter=${encodeURIComponent(JSON.stringify(uriStringLoc))}`
                );
                const promise2 = APIServices.post(API.GetAssuranceIndicator_UP(admin.id), { indicatorId: [promise3.data?.[0]?.id || 0], year: { startMonth: yrOptions.slice(-1)?.[0]?.startMonth, endMonth: yrOptions.slice(-1)?.[0]?.endMonth } })


                Promise.all([promise0, promise1, promise2]).then((values) => {
                    console.log(values[2].data)
                    let allframework = values[0].data.filter((i) => { return admin.information.report.includes(i.id) })
                    setIndiFilter((prev) => ({ ...prev, indicator: promise3.data?.[0]?.id, year: yrOptions.slice(-1)[0].name, framework: allframework.map(x => x.title) }))
                    const shapedSite = values[1]?.data
                        .map((item) => {
                            if (item.locationTwos) {
                                item.locationTwos = item.locationTwos.filter(
                                    (locationTwo) =>
                                        locationTwo.locationThrees &&
                                        locationTwo.locationThrees?.length > 0
                                );
                            }
                            return item;
                        })
                        .filter((item) => item.locationTwos && item.locationTwos?.length > 0);
                    setRawsitelist(shapedSite)
                    setLocList({ country: [{ name: 'All Countries', id: 0 }, ...shapedSite.map(location => ({ name: location.name, id: location.id }))] });

                    setAssFramework(allframework)

                    // Get the data and pre-compute summary values
                    const rawData = values[2].data || [];

                    // Get the indicator unit for consistent display
                    const indicatorUnit = rawData.length > 0 ? rawData[0].indicatorUnit || 'tCo2e' : 'tCo2e';

                    // Calculate enterprise summary (total of all computed values)
                    const enterpriseTotal = rawData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

                    // Process the data to include pre-computed summaries
                    const processedData = rawData.map(item => {
                        // Calculate entity summary for this location
                        const locationData = rawData.filter(dataItem => dataItem.locationId === item.locationId);
                        const entityTotal = locationData.reduce((sum, dataItem) => sum + (parseFloat(dataItem.computedValue) || 0), 0);

                        return {
                            ...item,
                            // Pre-computed values to avoid recalculation on render
                            _entityTotal: entityTotal,
                            _enterpriseTotal: enterpriseTotal,
                            _indicatorUnit: indicatorUnit
                        };
                    });

                    setMetricsData(processedData)
                    setMetricsDataBk(processedData)
                }).then(() => {
                    setLoad(false)
                })

            }
        } catch {

        }
    }
    function RenderTable() {

        return (
            <DataTable ref={tableRef} loading={load} value={metricsData} scrollable filters={{ entity: { matchMode: 'in', value: null }, indicatorType: { matchMode: 'in', value: null }, status: { matchMode: 'in', value: null }, reporting_period: { matchMode: 'in', value: null }, title: { matchMode: 'in', value: null }, emissionFactorName: { matchMode: 'in', value: null } }}>
                <Column field="entity" header={'Reporting Entity'} showFilterMatchModes={false}
                    filterElement={(options) => RowFilterTemplate(options, "entity")}
                    filter />
                <Column field="indicatorType" header={'Indicator Type'} showFilterMatchModes={false} body={(rowData) => { return rowData.indicatorType === 1 ? 'Standalone' : rowData.indicatorType === 2 ? 'Derived' : "NA" }}
                    filterElement={(options) => typeFilterTemplate(options)}
                    filter />
                <Column field="status" header={'Status'} showFilterMatchModes={false}
                    filterElement={(options) => RowFilterTemplate(options, "status")}
                    filter />
                <Column field="reporting_period" header={'Reporting Period'} showFilterMatchModes={false}
                    filterElement={(options) => RowFilterTemplate(options, "reporting_period")}
                    filter />
                <Column field="title" header={'Contributing Data Points'} showFilterMatchModes={false}
                    filterElement={(options) => RowFilterTemplate(options, "title")}
                    filter />
                <Column field="value" header={'Total Quantity'} />
                <Column field="unitOfMeasure" header={'Unit of Measure'} />
                <Column field="emissionFactorName" header={'Emission Factor (if applicable)'} showFilterMatchModes={false}
                    filterElement={(options) => RowFilterTemplate(options, "emissionFactorName")}
                    filter />
                <Column field="efkey" header={'Emission Factor ID'} />
                <Column field="emissionFactorValue" header={'Emission Factor Value of Co2e in kgCo2e'} />
                <Column field="emissionFactorCo2Value" header={'Emission Factor Value of CO2 in kgCo2e'} />
                <Column field="emissionFactorCh4Value" header={'Emission Factor Value of Ch4 in kgCo2e'} />
                <Column field="emissionFactorN2oValue" header={'Emission Factor Value of N2o in kgCo2e'} />
                <Column field="methodology" header={'Formula'} />
                <Column field="computedValue" header={'Computed Value in tCo2e'} />
                <Column field="computedCo2Value" header={'Computed Value in tCo2'} />
                <Column field="computedCh4Value" header={'Computed Value in tCh4'} />
                <Column field="computedN2oValue" header={'Computed Value in tN2o'} />
                <Column field="_entityTotal" header={'Entity Summary'} body={(rowData) => {
                    // Use pre-computed entity summary value
                    return rowData._entityTotal.toFixed(2) + ' ' + rowData._indicatorUnit;
                }} />
                <Column field="_enterpriseTotal" header={'Enterprise Summary'} body={(rowData) => {
                    // Use pre-computed enterprise summary value
                    return rowData._enterpriseTotal.toFixed(2) + ' ' + rowData._indicatorUnit;
                }} />
            </DataTable>
        );
    }
    const exportIndicatorReport = async () => {
        // Get the indicator unit from the first item for the headers
        const indicatorUnit = metricsData.length > 0 ? metricsData[0]._indicatorUnit || 'tCo2e' : 'tCo2e';

        const columns = [
            { field: 'entity', header: 'Reporting Entity' },
            { field: 'indicatorType', header: 'Indicator Type' },
            { field: 'status', header: 'Status' },
            { field: 'reporting_period', header: 'Reporting Period' },
            { field: 'title', header: 'Contributing Data Points' },
            { field: 'value', header: 'Total Quantity' },
            { field: 'unitOfMeasure', header: 'Unit of Measure' },
            { field: 'emissionFactorName', header: 'Emission Factor (if applicable)' },
            { field: 'efkey', header: 'Emission Factor ID' },
            { field: 'emissionFactorValue', header: 'Emission Factor Value of Co2e in kgCo2e' },
            { field: 'emissionFactorCo2Value', header: 'Emission Factor Value of CO2 in kgCo2e' },
            { field: 'emissionFactorCh4Value', header: 'Emission Factor Value of Ch4 in kgCo2e' },
            { field: 'emissionFactorN2oValue', header: 'Emission Factor Value of N2o in kgCo2e' },
            { field: 'methodology', header: 'Formula' },
            { field: 'computedValue', header: 'Computed Value in tCo2e' },
            { field: 'computedCo2Value', header: 'Computed Value in tCo2' },
            { field: 'computedCh4Value', header: 'Computed Value in tCh4' },
            { field: 'computedN2oValue', header: 'Computed Value in tN2o' },
            { field: '_entityTotal', header: `Entity Summary (${indicatorUnit})` },
            { field: '_enterpriseTotal', header: `Enterprise Summary (${indicatorUnit})` },
        ];
        // Prepare data for export
        const updatedData = tableRef?.current?.getVirtualScroller().props.items?.map((item) => {
            return {
                ...item,
                indicatorType:
                    item.indicatorType === 1
                        ? 'Standalone'
                        : item.indicatorType === 2
                            ? 'Derived'
                            : 'NA',
                // Keep the numeric values as is for proper Excel formatting
                // The unit will be added in the column header
            };
        });

        const workbook = await XlsxPopulate.fromBlankAsync();
        const sheet = workbook.sheet(0);

        // Write header
        columns.forEach((col, i) => {
            sheet.cell(1, i + 1).value(col.header).style({
                bold: true,
                fill: 'e0e0e0',
                border: true,
            });
        });

        // Write data rows
        updatedData.forEach((row, rowIndex) => {
            columns.forEach((col, colIndex) => {
                const cellValue = row[col.field];
                const cell = sheet.cell(rowIndex + 2, colIndex + 1);

                // Handle numeric fields specially to ensure they're stored as numbers
                if (col.field === 'computedValue' ||
                    col.field === 'computedCo2Value' ||
                    col.field === 'computedCh4Value' ||
                    col.field === 'computedN2oValue' ||
                    col.field === '_entityTotal' ||
                    col.field === '_enterpriseTotal') {
                    // Parse as number if it's a numeric field
                    const numValue = parseFloat(cellValue);
                    if (!isNaN(numValue)) {
                        cell.value(numValue);
                    } else {
                        cell.value(cellValue ?? '');
                    }
                } else {
                    // For other fields, use the value as is
                    cell.value(cellValue ?? '');
                }
            });
        });

        // Auto width
        columns.forEach((_, colIndex) => {
            sheet.column(colIndex + 1).width(25);
        });

        // Export the workbook
        const blob = await workbook.outputAsync();
        const blobUrl = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = (indidcatorlist.find(x => x.id === indifilter.indicator)?.title || '') + '_Data.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    const updateDataByIndicatorFilter = async (obj, val) => {
        let loc = { ...indifilter, [obj]: val }
        const year = yearOption.find(x => x.name === loc.year)
        setLoad(true)
        const promise2 = await APIServices.post(API.GetAssuranceIndicator_UP(admin.id), { indicatorId: [loc.indicator], framework: loc.framework , year: { startMonth: year.startMonth, endMonth: year.endMonth } })

        // Get the data and pre-compute summary values
        const rawData = promise2?.data || [];

        // Get the indicator unit for consistent display
        const indicatorUnit = rawData.length > 0 ? rawData[0].indicatorUnit || 'tCo2e' : 'tCo2e';

        // Calculate enterprise summary (total of all computed values)
        const enterpriseTotal = rawData.reduce((sum, item) => sum + (parseFloat(item.computedValue) || 0), 0);

        // Process the data to include pre-computed summaries
        const processedData = rawData.map(item => {
            // Calculate entity summary for this location
            const locationData = rawData.filter(dataItem => dataItem.locationId === item.locationId);
            const entityTotal = locationData.reduce((sum, dataItem) => sum + (parseFloat(dataItem.computedValue) || 0), 0);

            return {
                ...item,
                // Pre-computed values to avoid recalculation on render
                _entityTotal: entityTotal,
                _enterpriseTotal: enterpriseTotal,
                _indicatorUnit: indicatorUnit
            };
        });

        setMetricsData(processedData);
        setIndiFilter(loc);
        setLoad(false);
    }
    const RowFilterTemplate = (options, obj) => {

        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(metricsData.map((i) => i[obj]).filter(x => x)))}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const typeFilterTemplate = (options) => {
        let allentity = JSON.parse(JSON.stringify(metricsData))
        let locationOptions = Array.from(
            new Map(
                allentity
                    .map(id => [{ name: "Standalone", value: 1, name: "Derived", value: 2 }].find(x => x.value === id.indicatorType))
                    .filter(Boolean)
                    .map(item => [item.value, item])
            ).values()
        );
        return (
            <MultiSelect
                panelClassName={'hidefilter'}
                filter
                value={options.value}
                options={locationOptions}
                optionValue='value'
                optionLabel='name'
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: '14rem' }}
            />
        );

    }
    return (
        <>
            <p>
                This screen displays indicators for the selected reporting year,
                where "Indicator" refers to computed value
                included in reports or dashboards. Use the filters to select the
                year, category, and indicator to view. Only indicators that have
                completed the approval process are listed here; unapproved data
                points are not visible. For the selected indicator(s), the table
                shows all contributing source data, reporting entities, and, where
                applicable, the associated emission factors. Clicking on a data
                point will open its specific submission screen, providing full
                workflow details, attached evidences, and additional information.
            </p>
            <div className="col-12 align-items-end flex justify-content-end">
                <Button
                    disabled={metricsData?.length === 0}
                    onClick={() => {
                        exportIndicatorReport();
                    }}
                    label="Export Report"
                    icon="pi pi-download"
                    className="p-button-primary mr-3"
                />
            </div>
            <Box display="flex" gap={2} padding={2} alignItems="center">
                <FormControl sx={{ minWidth: 200 }}>
                    <label htmlFor="reporting-period-dropdown">
                        Reporting Year
                    </label>
                    <Dropdown
                        id="reporting-period-dropdown"
                        disabled={load}
                        value={indifilter.year}
                        options={[...yearOption]}
                        optionValue="name"
                        optionLabel="label"
                        onChange={(e) => updateDataByIndicatorFilter('year', e.value)}
                        placeholder="Select Reporting Year"
                    />
                </FormControl>
                <FormControl sx={{ minWidth: 200 }} >
                    <label htmlFor="category-dropdown">Framework</label>
                    <MultiSelect disabled={load} display="chip" style={{ width: 300 }} value={indifilter.framework} onChange={(e) => updateDataByIndicatorFilter('framework', e.value)} options={assFramework} optionLabel="title" optionValue="title"
                        filter={true} placeholder="Select" panelClassName={'hidefilter'} />
                </FormControl>

                <FormControl sx={{ minWidth: 200 }}>
                    <label htmlFor="datasource-dropdown">Select Indicator</label>
                    <Dropdown
                        id="indicator-types"
                        value={indifilter.indicator}
                        disabled={load}
                        filter
                        optionLabel="label"
                        optionValue="value"
                        options={indidcatorlist.map(x => ({ label: x.id + " : " + x.title, value: x.id }))}
                        onChange={(e) => updateDataByIndicatorFilter('indicator', e.value)}

                        placeholder="Select Type of Indicator"
                    />
                </FormControl>

            </Box>
            {RenderTable(metricsData)}
        </>
    )

}