import React, { useState, useMemo } from 'react';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { Tag } from 'primereact/tag';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { DateTime } from 'luxon';

const NonComplianceDetailsDialog = ({ 
    visible, 
    onHide, 
    nonComplianceData, 
    title = "Critical Non-Compliance Details",
    esgType = "All" // "Environmental", "Social", "Governance", or "All"
}) => {
    const [globalFilter, setGlobalFilter] = useState('');

    // Category labels mapping
    const getCategoryLabel = (categoryOfFinding) => {
        switch (categoryOfFinding) {
            case 1: return 'Good Practices';
            case 2: return 'Opportunity for Improvement';
            case 3: return 'Non-compliance';
            default: return 'Unknown';
        }
    };

    // Non-compliance type labels mapping
    const getNonComplianceLabel = (nonComplianceType) => {
        switch (nonComplianceType) {
            case 1: return 'Regulatory (Major)';
            case 2: return 'Regulatory (Minor)';
            case 3: return 'Minor';
            default: return 'Not Specified';
        }
    };

    // Status labels mapping
    const getStatusLabel = (status) => {
        switch (status) {
            case 1: return 'Open';
            case 2: return 'In Progress';
            case 3: return 'Completed';
            default: return 'Initiated';
        }
    };

    // ESG type labels mapping
    const getESGLabel = (esg) => {
        switch (esg) {
            case 1: return 'Environmental';
            case 2: return 'Social';
            case 3: return 'Governance';
            default: return 'Not Specified';
        }
    };

    // Date formatting template
    const dateTemplate = (rowData, field) => {
        const dateStr = rowData?.[field];
        return dateStr ? DateTime.fromISO(dateStr).toFormat('dd-MM-yyyy') : 'NA';
    };

    // Status template with colored tags
    const statusTemplate = (rowData) => {
        const status = getStatusLabel(rowData.status);
        const severity = rowData.status === 3 ? 'success' : 
                        rowData.status === 2 ? 'warning' : 'info';
        return <Tag value={status} severity={severity} />;
    };

    // Category template with colored tags
    const categoryTemplate = (rowData) => {
        const category = getCategoryLabel(rowData.categoryOfFinding);
        const severity = rowData.categoryOfFinding === 3 ? 'danger' : 
                        rowData.categoryOfFinding === 2 ? 'warning' : 'success';
        return <Tag value={category} severity={severity} />;
    };

    // Non-compliance type template
    const nonComplianceTypeTemplate = (rowData) => {
        if (rowData.categoryOfFinding !== 3) return 'N/A';
        const type = getNonComplianceLabel(rowData.nonComplianceType);
        const severity = rowData.nonComplianceType === 1 ? 'danger' : 
                        rowData.nonComplianceType === 2 ? 'warning' : 'info';
        return <Tag value={type} severity={severity} />;
    };

    // ESG type template
    const esgTemplate = (rowData) => {
        const esgLabel = getESGLabel(rowData.esg);
        const severity = rowData.esg === 1 ? 'success' : 
                        rowData.esg === 2 ? 'info' : 
                        rowData.esg === 3 ? 'warning' : 'secondary';
        return <Tag value={esgLabel} severity={severity} />;
    };

    // Description template with truncation
    const descriptionTemplate = (rowData) => {
        const description = rowData.description || 'No description available';
        return (
            <div title={description} style={{ maxWidth: '200px' }}>
                {description.length > 100 ? `${description.substring(0, 100)}...` : description}
            </div>
        );
    };

    // Finding template with truncation
    const findingTemplate = (rowData) => {
        const finding = rowData.finding || 'No finding specified';
        return (
            <div title={finding} style={{ maxWidth: '200px' }}>
                {finding.length > 80 ? `${finding.substring(0, 80)}...` : finding}
            </div>
        );
    };

    // Header template for search
    const header = (
        <div className="flex justify-content-between align-items-center">
            <h5 className="m-0">
                {title} ({nonComplianceData?.length || 0} records)
            </h5>
            <span className="p-input-icon-left">
                <i className="pi pi-search" />
                <InputText 
                    type="search" 
                    onInput={(e) => setGlobalFilter(e.target.value)} 
                    placeholder="Search..." 
                />
            </span>
        </div>
    );

    return (
        <Dialog
            header={
                <div className="flex align-items-center">
                    <i className="pi pi-exclamation-triangle mr-2" style={{ fontSize: '1.5rem', color: '#dc3545' }}></i>
                    <span className="font-bold">{title}</span>
                </div>
            }
            visible={visible}
            onHide={onHide}
            style={{ width: '95vw', maxWidth: '1400px' }}
            breakpoints={{ '960px': '98vw' }}
            className="non-compliance-details-dialog"
        >
            <Card>
                <DataTable
                    value={nonComplianceData}
                    paginator
                    rows={10}
                    rowsPerPageOptions={[10, 25, 50, 100]}
                    globalFilter={globalFilter}
                    header={header}
                    scrollable
                    scrollHeight="500px"
                    className="p-datatable-gridlines"
                    emptyMessage="No non-compliance records found."
                    removableSort
                >
                    <Column 
                        field="id" 
                        header="ID" 
                        sortable 
                        style={{ minWidth: '80px' }}
                    />
                    <Column 
                        field="finding" 
                        header="Finding" 
                        body={findingTemplate}
                        sortable 
                        style={{ minWidth: '200px' }}
                    />
                    <Column 
                        field="categoryOfFinding" 
                        header="Category" 
                        body={categoryTemplate}
                        sortable 
                        style={{ minWidth: '150px' }}
                    />
                    <Column 
                        field="nonComplianceType" 
                        header="Non-Compliance Type" 
                        body={nonComplianceTypeTemplate}
                        sortable 
                        style={{ minWidth: '180px' }}
                    />
                    <Column 
                        field="esg" 
                        header="ESG Type" 
                        body={esgTemplate}
                        sortable 
                        style={{ minWidth: '120px' }}
                    />
                    <Column 
                        field="description" 
                        header="Description" 
                        body={descriptionTemplate}
                        style={{ minWidth: '250px' }}
                    />
                    <Column 
                        field="vendorName" 
                        header="Supplier" 
                        sortable 
                        style={{ minWidth: '200px' }}
                    />
                    <Column 
                        field="vendorLocation" 
                        header="Location" 
                        sortable 
                        style={{ minWidth: '150px' }}
                    />
                    <Column 
                        field="status" 
                        header="Status" 
                        body={statusTemplate}
                        sortable 
                        style={{ minWidth: '120px' }}
                    />
                    <Column 
                        field="created_on" 
                        header="Created On" 
                        body={(rowData) => dateTemplate(rowData, 'created_on')}
                        sortable 
                        style={{ minWidth: '120px' }}
                    />
                    <Column 
                        field="dueDate" 
                        header="Due Date" 
                        body={(rowData) => dateTemplate(rowData, 'dueDate')}
                        sortable 
                        style={{ minWidth: '120px' }}
                    />
                </DataTable>
            </Card>
        </Dialog>
    );
};

export default NonComplianceDetailsDialog;
