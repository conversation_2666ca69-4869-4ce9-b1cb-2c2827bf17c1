import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Card } from "primereact/card";
import { <PERSON><PERSON> } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const MSIActionItemsChart = ({ supplyData = [] }) => {
  const [activeMode, setActiveMode] = useState(true);
  const [chartData, setChartData] = useState([]);

  // Process supply data to extract action items by category
  useEffect(() => {
    const fetchActionItemsData = async () => {
      try {
        const filter = {
          order: ["created_on DESC"],
          include: [
            { relation: "supplierActions" },
            { relation: "auditorAssignmentSubmission" }
          ]
        };

        const res = await APIServices.get(
          API.Supplier_assessment_assignment + `?filter=${encodeURIComponent(JSON.stringify(filter))}`
        );

        const allAssignments = Array.isArray(res.data) ? res.data : [];

        // Group by vendorCode to get latest entry per vendor
        const groupedByVendor = allAssignments.reduce((acc, item) => {
          if (!item.vendorCode) return acc;
          if (!acc[item.vendorCode]) acc[item.vendorCode] = [];
          acc[item.vendorCode].push(item);
          return acc;
        }, {});

        // Initialize counters
        let goodPractices = 0;
        let opportunityImprovement = 0;
        let regulatoryMajor = 0;
        let regulatoryMinor = 0;
        let minorNonCompliance = 0;

        Object.values(groupedByVendor).forEach(assignments => {
          // Sort to get the latest
          const latest = assignments.sort((a, b) =>
            new Date(b.created_on) - new Date(a.created_on)
          )[0];

          const actions = latest.supplierActions || [];

          actions.forEach(action => {
            switch (action.categoryOfFinding) {
              case 1:
                goodPractices++;
                break;
              case 2:
                opportunityImprovement++;
                break;
              case 3:
                // Non-compliance - check type
                switch (action.nonComplianceType) {
                  case 1:
                    regulatoryMajor++;
                    break;
                  case 2:
                    regulatoryMinor++;
                    break;
                  case 3:
                    minorNonCompliance++;
                    break;
                  default:
                    minorNonCompliance++; // Default to minor if type not specified
                    break;
                }
                break;
              default:
                break;
            }
          });
        });

        const processedData = [
          { category: "Good Practices", count: goodPractices, color: "#22C55E" },
          { category: "Opportunity for Improvement", count: opportunityImprovement, color: "#3B82F6" },
          { category: "Regulatory Major Non-compliance", count: regulatoryMajor, color: "#EF4444" },
          { category: "Regulatory Minor Non-compliance", count: regulatoryMinor, color: "#F59E0B" },
          { category: "Minor Non-compliance", count: minorNonCompliance, color: "#8B5CF6" }
        ];

        console.log('MSIActionItemsChart - Final processed data:', processedData);
        console.log('MSIActionItemsChart - Action counts:', {
          goodPractices,
          opportunityImprovement,
          regulatoryMajor,
          regulatoryMinor,
          minorNonCompliance
        });

        setChartData(processedData);
      } catch (error) {
        console.error('Error fetching action items data:', error);
        // Fallback to mock data on error
        const mockData = [
          { category: "Good Practices", count: 15, color: "#22C55E" },
          { category: "Opportunity for Improvement", count: 8, color: "#3B82F6" },
          { category: "Regulatory Major Non-compliance", count: 3, color: "#EF4444" },
          { category: "Regulatory Minor Non-compliance", count: 5, color: "#F59E0B" },
          { category: "Minor Non-compliance", count: 2, color: "#8B5CF6" }
        ];
        setChartData(mockData);
      }
    };

    fetchActionItemsData();
  }, []);

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '10px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>{label}</p>
          <p style={{ margin: 0, color: payload[0].color }}>
            Count: {payload[0].value}
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom cell colors for bars
  const CustomizedBar = (props) => {
    const { payload, ...rest } = props;
    return <Bar {...rest} fill={payload?.color || "#3B82F6"} />;
  };

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
        <h3 style={{ margin: 0, borderBottom: "2px solid #007bff", paddingBottom: "5px" }}>
          Action Items by Category
        </h3>
        <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
          <Button
            icon={activeMode ? "pi pi-table" : "pi pi-chart-bar"}
            className="p-button-text"
            onClick={() => setActiveMode(!activeMode)}
            tooltip={activeMode ? "Switch to Table View" : "Switch to Chart View"}
          />
        </div>
      </div>

      {activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="category" 
              angle={-45}
              textAnchor="end"
              height={100}
              fontSize={12}
            />
            <YAxis domain={[0, 'dataMax + 2']} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar
              dataKey="count"
              name="Number of Actions"
              fill="#8884d8"
              minPointSize={5}
            />
          </BarChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={chartData} paginator rows={10} sortMode="multiple">
            <Column field="category" header="Category" sortable />
            <Column 
              field="count" 
              header="Count" 
              sortable 
              body={(rowData) => (
                <span style={{ 
                  color: rowData.color, 
                  fontWeight: 'bold' 
                }}>
                  {rowData.count}
                </span>
              )}
            />
          </DataTable>
        </div>
      )}

      {/* Summary */}
      <div style={{ 
        marginTop: "20px", 
        padding: "15px", 
        backgroundColor: "#f8f9fa", 
        borderRadius: "5px" 
      }}>
        <h4 style={{ margin: "0 0 10px 0" }}>Summary</h4>
        <div style={{ display: "flex", justifyContent: "space-between", flexWrap: "wrap" }}>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Total Actions</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#333" }}>
              {chartData.reduce((sum, item) => sum + item.count, 0)}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Non-Compliance</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#EF4444" }}>
              {chartData
                .filter(item => item.category.includes("Non-compliance"))
                .reduce((sum, item) => sum + item.count, 0)
              }
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Improvements</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#3B82F6" }}>
              {chartData.find(item => item.category === "Opportunity for Improvement")?.count || 0}
            </p>
          </div>
          <div style={{ textAlign: "center", minWidth: "100px" }}>
            <p style={{ margin: "5px 0", fontSize: "14px", color: "#666" }}>Good Practices</p>
            <p style={{ margin: "0", fontSize: "20px", fontWeight: "bold", color: "#22C55E" }}>
              {chartData.find(item => item.category === "Good Practices")?.count || 0}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default MSIActionItemsChart;
